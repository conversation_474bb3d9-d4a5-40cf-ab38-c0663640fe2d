<script lang="ts" setup>
  import { reactive, ref } from 'vue';

  import { message } from 'ant-design-vue';

  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  import emitter from './usermitt';

  defineOptions({
    name: 'AddSubjectPop',
  });

  const emits = defineEmits(['refresh']);

  const formRef = ref<any>(null);
  const { pureSubjectOptions } = useAccountSubjects();
  const loading = ref(false);
  const visible = ref(false);

  const formState = reactive({
    code: '404', // 科目编码，默认404
    name: '', // 科目名称
    parentId: '', // 上级科目id
  });

  // 重置表单
  const resetForm = () => {
    formState.code = '404';
    formState.name = '';
    formState.parentId = '';
  };

  // 打开弹框
  const open = (parentSubject?: { value: string }) => {
    resetForm();
    if (parentSubject && parentSubject.value) {
      formState.parentId = parentSubject.value;
    }
    visible.value = true;
  };
  // 确认保存
  const handleOk = async () => {
    try {
      loading.value = true;
      await formRef.value.validate();

      // 查找上级科目以获取其全名
      const parentSubject = pureSubjectOptions.value.find(
        (option) => option.value === formState.parentId,
      ) as any;
      const parentFullName = parentSubject ? parentSubject.name : '';

      // 构建新科目对象
      const newSubject = {
        // 为了与现有类型兼容，我们添加一些默认属性
        assistAccounting: null,
        balance: 0,
        balanceDirection: 'debit', // 或 'credit'，根据需要设置默认值
        code: formState.code,
        currency: null,
        fullName: parentFullName
          ? `${parentFullName}_${formState.name}`
          : formState.name,
        id: Date.now().toString(), // 使用时间戳作为唯一ID
        isForCurrency: false,
        level: parentSubject ? (parentSubject.level || 1) + 1 : 1,
        name: formState.name,
        text: `${formState.code} ${
          parentFullName
            ? `${parentFullName}_${formState.name}`
            : formState.name
        }`,
        useAssistant: false, // 默认不启用辅助核算
        value: Date.now().toString(), // 确保value存在
        children: [],
      };

      message.success('新增科目成功');
      // 通知凭证页面刷新科目数据
      emitter.emit('account_voucher_subject_added', newSubject);
      // 通知父组件刷新
      emits('refresh');
      // 关闭弹框
      visible.value = false;
    } catch (error) {
      console.error('新增科目失败:', error);
      message.error('新增科目失败');
    } finally {
      loading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    visible.value = false;
  };

  // 暴露方法给父组件
  defineExpose({
    open,
  });
</script>
<template>
  <a-modal
    :visible="visible"
    title="新增科目"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      :model="formState"
      autocomplete="off"
      ref="formRef"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item
        label="上级科目"
        name="parentId"
        :rules="[{ required: true, message: '请选择上级科目' }]"
      >
        <a-select
          v-model:value="formState.parentId"
          placeholder="请选择上级科目"
          show-search
          :field-names="{
            label: 'label',
            value: 'value',
          }"
          :options="pureSubjectOptions"
        />
      </a-form-item>
      <a-form-item label="科目编码" name="code">
        <a-input
          v-model:value="formState.code"
          placeholder="默认404"
          disabled
        />
      </a-form-item>
      <a-form-item
        label="科目名称"
        name="name"
        :rules="[{ required: true, message: '请输入科目名称' }]"
      >
        <a-input v-model:value="formState.name" placeholder="请输入科目名称" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<style lang="scss" scoped>
  .btn {
    text-align: center;
  }

  .reminder {
    font-size: 10px;
    color: red;
  }
</style>
